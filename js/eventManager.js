import GameState from './gameState';

class EventManager {
  constructor() {
    this.events = {
      childhood: [
        { id: 1, description: '学习诗词', effects: { intelligence: 5 } },
        { id: 2, description: '参加武术训练', effects: { health: 5 } }
      ],
      youth: [
        { id: 3, description: '结交名士', effects: { morality: 5, intelligence: 3 } },
        { id: 4, description: '游历名山', effects: { health: 3 } }
      ],
      adulthood: [
        { id: 5, description: '参加科举考试', effects: { intelligence: 10, wealth: 5 } },
        { id: 6, description: '经营家业', effects: { wealth: 10 } }
      ],
      oldage: [
        { id: 7, description: '著书立说', effects: { intelligence: 5, morality: 5 } },
        { id: 8, description: '隐居山林', effects: { health: 5 } }
      ]
    };
  }

  // 获取当前年龄阶段可触发的事件
  getAvailableEvents() {
    return this.events[GameState.lifeStage] || [];
  }

  // 触发事件，更新玩家属性
  triggerEvent(eventId) {
    const event = Object.values(this.events).flat().find(e => e.id === eventId);
    if (event) {
      for (const [attr, value] of Object.entries(event.effects)) {
        GameState.updateAttribute(attr, value);
      }
      GameState.recordEvent(event.description);
    }
  }
}

export default new EventManager();