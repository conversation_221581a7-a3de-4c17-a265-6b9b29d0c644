import GameState from './gameState.js';

export default class EventManager {
  constructor() {
    // 李白人生事件数据库
    this.events = {
      // 童年期事件 (0-15岁)
      childhood: [
        {
          id: 'child_001',
          type: 'key_event',
          title: '初学诗书',
          description: '父亲李客教你读书识字，你展现出过人的天赋',
          choices: [
            {
              text: '专心学习经典',
              effects: { intelligence: 8, morality: 5, poetry: 3 },
              consequences: '你在经典学习上打下了坚实基础'
            },
            {
              text: '更喜欢读诗词',
              effects: { poetry: 10, intelligence: 3, fame: 2 },
              consequences: '你对诗词产生了浓厚兴趣'
            }
          ],
          conditions: { age: 5 },
          location: '绵州昌隆'
        },
        {
          id: 'child_002',
          type: 'random_event',
          title: '遇见道士',
          description: '在山中游玩时遇到一位神秘道士，他看出你的不凡',
          choices: [
            {
              text: '跟随道士学习',
              effects: { morality: 10, health: 5, intelligence: 3 },
              consequences: '道士传授了你一些修身养性的方法',
              unlocks: ['youth_dao_path']
            },
            {
              text: '礼貌拒绝',
              effects: { morality: 3 },
              consequences: '你保持了应有的礼貌，但错过了机缘'
            }
          ],
          probability: 0.3,
          conditions: { age: 10 }
        },
        {
          id: 'child_003',
          type: 'choice_event',
          title: '家庭迁移',
          description: '父亲考虑举家迁往蜀中，询问你的意见',
          choices: [
            {
              text: '支持迁移',
              effects: { intelligence: 5, poetry: 5 },
              consequences: '你们迁居到了文化更加繁荣的蜀中',
              location_change: '蜀中'
            },
            {
              text: '希望留下',
              effects: { morality: 3, health: 3 },
              consequences: '你们继续在故乡生活'
            }
          ],
          conditions: { age: 12 }
        }
      ],

      // 青年期事件 (16-25岁)
      youth: [
        {
          id: 'youth_001',
          type: 'key_event',
          title: '初入江湖',
          description: '你决定离开家乡，开始自己的人生旅程',
          choices: [
            {
              text: '前往长安求学',
              effects: { intelligence: 10, fame: 5, wealth: -5 },
              consequences: '你来到了大唐的政治文化中心',
              location_change: '长安'
            },
            {
              text: '游历山川',
              effects: { poetry: 15, health: 8, morality: 5 },
              consequences: '你选择了游历天下，增长见识',
              unlocks: ['travel_events']
            },
            {
              text: '拜师学艺',
              effects: { intelligence: 12, poetry: 8, morality: 8 },
              consequences: '你找到了一位名师指导',
              unlocks: ['master_events']
            }
          ],
          conditions: { age: 18 },
          mandatory: true
        },
        {
          id: 'youth_002',
          type: 'random_event',
          title: '酒楼偶遇',
          description: '在酒楼中偶遇一群文人雅士，他们正在论诗',
          choices: [
            {
              text: '主动参与讨论',
              effects: { fame: 8, poetry: 5, intelligence: 3 },
              consequences: '你的才华让众人刮目相看',
              relationships: { '文人圈': 10 }
            },
            {
              text: '默默倾听',
              effects: { intelligence: 5, morality: 3 },
              consequences: '你学到了很多，但没有展现自己'
            },
            {
              text: '离开酒楼',
              effects: { morality: 2 },
              consequences: '你选择了独善其身'
            }
          ],
          probability: 0.4,
          conditions: { poetry: 30 }
        },
        {
          id: 'youth_003',
          type: 'choice_event',
          title: '科举考试',
          description: '朝廷开科取士，你可以参加科举考试',
          choices: [
            {
              text: '参加考试',
              effects: { intelligence: 5, fame: 10, wealth: 15 },
              consequences: '你通过了科举考试，获得了功名',
              unlocks: ['official_path'],
              conditions: { intelligence: 60 }
            },
            {
              text: '放弃科举',
              effects: { poetry: 10, morality: 5 },
              consequences: '你选择了自由的文人生活',
              unlocks: ['poet_path']
            }
          ],
          conditions: { age: 22, intelligence: 50 }
        }
      ],

      // 壮年期事件 (26-45岁)
      adulthood: [
        {
          id: 'adult_001',
          type: 'key_event',
          title: '诗名渐起',
          description: '你的诗才开始被世人认可，名声渐起',
          choices: [
            {
              text: '继续专心创作',
              effects: { poetry: 20, fame: 15, intelligence: 5 },
              consequences: '你的诗歌水平达到了新的高度'
            },
            {
              text: '寻求官职',
              effects: { wealth: 15, fame: 10, morality: -5 },
              consequences: '你开始寻求在朝廷的发展机会',
              unlocks: ['court_events']
            },
            {
              text: '游历天下',
              effects: { poetry: 15, health: 10, fame: 8 },
              consequences: '你选择继续游历，寻找创作灵感'
            }
          ],
          conditions: { age: 28, poetry: 50 },
          mandatory: true
        },
        {
          id: 'adult_002',
          type: 'random_event',
          title: '遇见贺知章',
          description: '在长安遇见了著名诗人贺知章，他对你的诗大加赞赏',
          choices: [
            {
              text: '虚心请教',
              effects: { poetry: 15, intelligence: 8, fame: 12 },
              consequences: '贺知章称你为"谪仙人"，你名声大噪',
              relationships: { '贺知章': 20 },
              achievements: ['谪仙人']
            },
            {
              text: '保持谦逊',
              effects: { morality: 10, poetry: 8, fame: 5 },
              consequences: '你的谦逊赢得了更多人的尊重'
            }
          ],
          probability: 0.6,
          conditions: { poetry: 70, location: '长安' }
        }
      ],

      // 中年期事件 (46-60岁)
      middleAge: [
        {
          id: 'middle_001',
          type: 'key_event',
          title: '人生转折',
          description: '你已到中年，需要为后半生做出重要选择',
          choices: [
            {
              text: '继续仕途',
              effects: { wealth: 20, fame: 10, morality: -10 },
              consequences: '你选择在官场继续发展',
              conditions: { wealth: 50 }
            },
            {
              text: '归隐山林',
              effects: { health: 15, morality: 15, poetry: 10 },
              consequences: '你选择了隐居生活',
              unlocks: ['hermit_events']
            },
            {
              text: '专心著述',
              effects: { poetry: 25, intelligence: 15, fame: 20 },
              consequences: '你决定将毕生所学著成文字'
            }
          ],
          conditions: { age: 50 },
          mandatory: true
        }
      ],

      // 晚年期事件 (61-80岁)
      oldAge: [
        {
          id: 'old_001',
          type: 'key_event',
          title: '回首往昔',
          description: '年老的你回顾自己的一生，思考人生的意义',
          choices: [
            {
              text: '满足于现状',
              effects: { morality: 10, health: 5 },
              consequences: '你对自己的人生感到满意'
            },
            {
              text: '仍有遗憾',
              effects: { poetry: 10, intelligence: 5 },
              consequences: '你觉得还有未完成的心愿'
            }
          ],
          conditions: { age: 65 },
          mandatory: true
        }
      ]
    };

    // 特殊事件链
    this.eventChains = {
      'youth_dao_path': ['youth_dao_001', 'youth_dao_002'],
      'travel_events': ['travel_001', 'travel_002', 'travel_003'],
      'master_events': ['master_001', 'master_002'],
      'official_path': ['official_001', 'official_002'],
      'poet_path': ['poet_001', 'poet_002'],
      'court_events': ['court_001', 'court_002'],
      'hermit_events': ['hermit_001', 'hermit_002']
    };

    // 已解锁的事件链
    this.unlockedChains = [];

    // 已触发的事件
    this.triggeredEvents = [];
  }

  // 获取当前年龄阶段可触发的事件
  getAvailableEvents() {
    const currentStageEvents = this.events[GameState.lifeStage] || [];
    const availableEvents = [];

    for (const event of currentStageEvents) {
      // 检查事件是否已触发
      if (this.triggeredEvents.includes(event.id)) {
        continue;
      }

      // 检查事件触发条件
      if (this.checkEventConditions(event)) {
        // 检查随机事件的概率
        if (event.type === 'random_event') {
          if (Math.random() < (event.probability || 1)) {
            availableEvents.push(event);
          }
        } else {
          availableEvents.push(event);
        }
      }
    }

    return availableEvents;
  }

  // 检查事件触发条件
  checkEventConditions(event) {
    if (!event.conditions) return true;

    const conditions = event.conditions;

    // 检查年龄条件
    if (conditions.age && GameState.age < conditions.age) {
      return false;
    }

    // 检查属性条件
    for (const [attr, value] of Object.entries(conditions)) {
      if (attr !== 'age' && attr !== 'location' && GameState[attr] < value) {
        return false;
      }
    }

    // 检查位置条件
    if (conditions.location && GameState.currentLocation !== conditions.location) {
      return false;
    }

    return true;
  }

  // 触发事件
  triggerEvent(eventId, choiceIndex = 0) {
    const event = this.findEventById(eventId);
    if (!event) {
      console.error('事件未找到:', eventId);
      return null;
    }

    // 标记事件已触发
    this.triggeredEvents.push(eventId);

    // 获取选择的结果
    const choice = event.choices[choiceIndex];
    if (!choice) {
      console.error('选择索引无效:', choiceIndex);
      return null;
    }

    // 应用效果
    this.applyEventEffects(choice.effects);

    // 处理后续效果
    this.handleEventConsequences(choice);

    // 记录事件到历史
    GameState.recordEvent({
      type: 'event_triggered',
      eventId: eventId,
      title: event.title,
      choice: choice.text,
      consequences: choice.consequences,
      age: GameState.age
    });

    return {
      event: event,
      choice: choice,
      effects: choice.effects
    };
  }

  // 应用事件效果
  applyEventEffects(effects) {
    for (const [attr, value] of Object.entries(effects)) {
      GameState.updateAttribute(attr, value);
    }
  }

  // 处理事件后续效果
  handleEventConsequences(choice) {
    // 解锁新的事件链
    if (choice.unlocks) {
      for (const chain of choice.unlocks) {
        if (!this.unlockedChains.includes(chain)) {
          this.unlockedChains.push(chain);
        }
      }
    }

    // 更新人际关系
    if (choice.relationships) {
      for (const [person, value] of Object.entries(choice.relationships)) {
        GameState.updateRelationship(person, value);
      }
    }

    // 添加成就
    if (choice.achievements) {
      for (const achievement of choice.achievements) {
        GameState.addAchievement(achievement);
      }
    }

    // 改变位置
    if (choice.location_change) {
      GameState.changeLocation(choice.location_change);
    }
  }

  // 根据ID查找事件
  findEventById(eventId) {
    for (const stageEvents of Object.values(this.events)) {
      for (const event of stageEvents) {
        if (event.id === eventId) {
          return event;
        }
      }
    }
    return null;
  }

  // 获取强制事件（关键人生节点）
  getMandatoryEvents() {
    const currentStageEvents = this.events[GameState.lifeStage] || [];
    return currentStageEvents.filter(event =>
      event.mandatory &&
      !this.triggeredEvents.includes(event.id) &&
      this.checkEventConditions(event)
    );
  }

  // 获取可选事件
  getOptionalEvents() {
    const currentStageEvents = this.events[GameState.lifeStage] || [];
    return currentStageEvents.filter(event =>
      !event.mandatory &&
      !this.triggeredEvents.includes(event.id) &&
      this.checkEventConditions(event)
    );
  }

  // 重置事件管理器
  reset() {
    this.unlockedChains = [];
    this.triggeredEvents = [];
  }

  // 获取事件统计
  getEventStats() {
    return {
      totalTriggered: this.triggeredEvents.length,
      unlockedChains: this.unlockedChains.length,
      availableEvents: this.getAvailableEvents().length
    };
  }
}

//export default new EventManager();