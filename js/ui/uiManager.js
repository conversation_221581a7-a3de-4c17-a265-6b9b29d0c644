import GameState from '../gameState.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';
import resourceManager from '../resourceManager.js';

class UIManager {
  constructor(ctx) {
    this.ctx = ctx;
    this.currentScreen = 'start'; // start, main, event, attributes, history, ending
    this.buttons = [];
    this.selectedButton = 0;
    this.animationFrame = 0;
    
    // UI样式配置
    this.styles = {
      colors: {
        primary: '#8B4513',      // 古铜色
        secondary: '#DAA520',    // 金色
        background: '#F5E6D3',   // 米色
        text: '#2F1B14',         // 深棕色
        accent: '#CD853F',       // 沙棕色
        highlight: '#FFD700'     // 金黄色
      },
      fonts: {
        title: '28px serif',
        subtitle: '20px serif',
        body: '16px serif',
        small: '14px serif'
      },
      spacing: {
        margin: 20,
        padding: 15,
        buttonHeight: 50,
        lineHeight: 25
      }
    };
  }

  // 渲染当前界面
  render() {
    this.animationFrame++;
    this.clearScreen();

    switch (this.currentScreen) {
      case 'loading':
        this.renderLoadingScreen();
        break;
      case 'start':
        this.renderStartScreen();
        break;
      case 'main':
        this.renderMainScreen();
        break;
      case 'event':
        this.renderEventScreen();
        break;
      case 'attributes':
        this.renderAttributesScreen();
        break;
      case 'history':
        this.renderHistoryScreen();
        break;
      case 'ending':
        this.renderEndingScreen();
        break;
    }
  }

  // 渲染事件选择界面
  renderEventScreen() {
    const margin = this.styles.spacing.margin;

    if (!this.currentEvent) {
      this.drawText('没有可触发的事件', margin, 100, this.styles.fonts.subtitle, this.styles.colors.text);
      this.buttons = [
        { text: '返回', x: margin, y: SCREEN_HEIGHT - 80, width: 100, height: 40, action: 'backToMain' }
      ];
      this.renderButtons();
      return;
    }

    // 绘制事件标题
    this.drawText(this.currentEvent.title, margin, 80, this.styles.fonts.title, this.styles.colors.primary);

    // 绘制事件描述
    this.drawMultilineText(this.currentEvent.description, margin, 120, SCREEN_WIDTH - margin * 2, this.styles.fonts.body, this.styles.colors.text);

    // 绘制选择选项
    const choicesStartY = 200;
    this.buttons = [];

    this.currentEvent.choices.forEach((choice, index) => {
      const y = choicesStartY + index * 80;
      const buttonHeight = 60;

      // 选择按钮
      this.buttons.push({
        text: choice.text,
        x: margin,
        y: y,
        width: SCREEN_WIDTH - margin * 2,
        height: buttonHeight,
        action: `selectChoice_${index}`,
        choice: choice
      });
    });

    // 添加返回按钮
    this.buttons.push({
      text: '返回',
      x: margin,
      y: SCREEN_HEIGHT - 80,
      width: 100,
      height: 40,
      action: 'backToMain'
    });

    this.renderEventButtons();
  }

  // 渲染事件按钮（特殊样式）
  renderEventButtons() {
    this.buttons.forEach((button, index) => {
      const isSelected = index === this.selectedButton;
      const isChoice = button.action.startsWith('selectChoice_');

      if (isChoice) {
        // 选择按钮样式
        this.ctx.fillStyle = isSelected ? this.styles.colors.secondary : this.styles.colors.background;
        this.ctx.fillRect(button.x, button.y, button.width, button.height);

        // 边框
        this.ctx.strokeStyle = isSelected ? this.styles.colors.primary : this.styles.colors.accent;
        this.ctx.lineWidth = isSelected ? 3 : 1;
        this.ctx.strokeRect(button.x, button.y, button.width, button.height);

        // 选择文字
        const textColor = isSelected ? '#FFFFFF' : this.styles.colors.text;
        this.drawText(button.text, button.x + 15, button.y + 25, this.styles.fonts.body, textColor);

        // 显示选择效果预览
        if (button.choice && button.choice.effects) {
          const effectsText = this.formatEffects(button.choice.effects);
          this.drawText(effectsText, button.x + 15, button.y + 45, this.styles.fonts.small, this.styles.colors.secondary);
        }
      } else {
        // 普通按钮
        this.ctx.fillStyle = isSelected ? this.styles.colors.secondary : this.styles.colors.accent;
        this.ctx.fillRect(button.x, button.y, button.width, button.height);

        this.ctx.strokeStyle = this.styles.colors.primary;
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(button.x, button.y, button.width, button.height);

        const textColor = isSelected ? '#FFFFFF' : this.styles.colors.text;
        this.drawText(button.text, button.x + button.width / 2, button.y + button.height / 2 + 5,
                     this.styles.fonts.body, textColor, 'center');
      }
    });
  }

  // 渲染属性详情界面
  renderAttributesScreen() {
    const margin = this.styles.spacing.margin;

    this.drawText('属性详情', margin, 50, this.styles.fonts.title, this.styles.colors.primary);

    const attributes = [
      { name: '健康', value: GameState.health, description: '影响寿命和活动能力' },
      { name: '才智', value: GameState.intelligence, description: '影响学习和创作能力' },
      { name: '道德', value: GameState.morality, description: '影响社会声誉和人际关系' },
      { name: '财富', value: GameState.wealth, description: '影响生活质量和选择机会' },
      { name: '名声', value: GameState.fame, description: '影响社会地位和机遇' },
      { name: '诗才', value: GameState.poetry, description: '李白特有，影响文学成就' }
    ];

    let y = 100;
    attributes.forEach(attr => {
      this.drawText(`${attr.name}: ${attr.value}`, margin, y, this.styles.fonts.subtitle, this.styles.colors.primary);
      this.drawText(attr.description, margin + 20, y + 25, this.styles.fonts.body, this.styles.colors.text);
      y += 60;
    });

    // 显示成就
    if (GameState.achievements.length > 0) {
      this.drawText('已获得成就:', margin, y + 20, this.styles.fonts.subtitle, this.styles.colors.primary);
      GameState.achievements.forEach((achievement, index) => {
        this.drawText(`• ${achievement}`, margin + 20, y + 50 + index * 25, this.styles.fonts.body, this.styles.colors.secondary);
      });
    }

    this.buttons = [
      { text: '返回', x: margin, y: SCREEN_HEIGHT - 80, width: 100, height: 40, action: 'backToMain' }
    ];
    this.renderButtons();
  }

  // 渲染历史回顾界面
  renderHistoryScreen() {
    const margin = this.styles.spacing.margin;

    this.drawText('人生历程', margin, 50, this.styles.fonts.title, this.styles.colors.primary);

    const recentEvents = GameState.history.slice(-10); // 显示最近10个事件
    let y = 100;

    recentEvents.forEach(event => {
      if (event.type === 'event_triggered') {
        this.drawText(`${event.age}岁: ${event.title}`, margin, y, this.styles.fonts.body, this.styles.colors.primary);
        this.drawText(`选择: ${event.choice}`, margin + 20, y + 20, this.styles.fonts.small, this.styles.colors.text);
        y += 50;
      }
    });

    this.buttons = [
      { text: '返回', x: margin, y: SCREEN_HEIGHT - 80, width: 100, height: 40, action: 'backToMain' }
    ];
    this.renderButtons();
  }

  // 渲染结局界面
  renderEndingScreen() {
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;

    this.drawText('人生落幕', centerX, centerY - 100, this.styles.fonts.title, this.styles.colors.primary, 'center');

    if (GameState.endingType) {
      this.drawText(GameState.endingType.title, centerX, centerY - 50, this.styles.fonts.subtitle, this.styles.colors.secondary, 'center');
      this.drawMultilineText(GameState.endingType.description, centerX - 150, centerY, 300, this.styles.fonts.body, this.styles.colors.text, 'center');
    }

    this.buttons = [
      { text: '重新开始', x: centerX - 100, y: centerY + 100, width: 200, height: 50, action: 'restart' }
    ];
    this.renderButtons();
  }

  // 清空屏幕
  clearScreen() {
    // 尝试绘制背景图片
    const bgImage = resourceManager.getImage('background');
    if (bgImage) {
      this.ctx.drawImage(bgImage, 0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    } else {
      // 如果背景图片未加载，绘制渐变背景
      const gradient = this.ctx.createLinearGradient(0, 0, 0, SCREEN_HEIGHT);
      gradient.addColorStop(0, '#E6D7C3');
      gradient.addColorStop(1, '#D4C4A8');

      this.ctx.fillStyle = gradient;
      this.ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    }
  }

  // 渲染加载界面
  renderLoadingScreen() {
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;

    // 绘制标题
    this.drawText('如果能重来', centerX, centerY - 100, this.styles.fonts.title, this.styles.colors.primary, 'center');

    // 绘制加载进度
    const progress = resourceManager.getProgress();
    const progressText = `加载中... ${Math.round(progress * 100)}%`;
    this.drawText(progressText, centerX, centerY, this.styles.fonts.body, this.styles.colors.text, 'center');

    // 绘制进度条
    const barWidth = 300;
    const barHeight = 20;
    const barX = centerX - barWidth / 2;
    const barY = centerY + 30;

    // 进度条背景
    this.ctx.fillStyle = '#E0E0E0';
    this.ctx.fillRect(barX, barY, barWidth, barHeight);

    // 进度条
    this.ctx.fillStyle = this.styles.colors.secondary;
    this.ctx.fillRect(barX, barY, barWidth * progress, barHeight);

    // 进度条边框
    this.ctx.strokeStyle = this.styles.colors.primary;
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(barX, barY, barWidth, barHeight);
  }

  // 渲染开始界面
  renderStartScreen() {
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;

    // 绘制标题
    this.drawText('如果能重来', centerX, centerY - 150, this.styles.fonts.title, this.styles.colors.primary, 'center');
    this.drawText('李白的人生选择', centerX, centerY - 110, this.styles.fonts.subtitle, this.styles.colors.secondary, 'center');

    // 绘制描述
    this.drawText('以李白的视角，在人生的关键阶段做出决策', centerX, centerY - 50, this.styles.fonts.body, this.styles.colors.text, 'center');
    this.drawText('走向不同的人生结局', centerX, centerY - 25, this.styles.fonts.body, this.styles.colors.text, 'center');

    // 绘制按钮
    this.buttons = [
      { text: '开始李白的一生', x: centerX - 100, y: centerY + 50, width: 200, height: 50, action: 'startNewGame' },
      { text: '继续之前的人生', x: centerX - 100, y: centerY + 120, width: 200, height: 50, action: 'loadGame' }
    ];

    this.renderButtons();
  }

  // 渲染主游戏界面
  renderMainScreen() {
    const margin = this.styles.spacing.margin;
    
    // 绘制顶部信息栏
    this.renderTopBar();
    
    // 绘制属性面板
    this.renderAttributePanel();
    
    // 绘制当前状态
    this.renderCurrentStatus();
    
    // 绘制操作按钮
    this.buttons = [
      { text: '查看可触发事件', x: margin, y: SCREEN_HEIGHT - 150, width: 150, height: 40, action: 'showEvents' },
      { text: '查看属性详情', x: margin + 160, y: SCREEN_HEIGHT - 150, width: 150, height: 40, action: 'showAttributes' },
      { text: '查看历史', x: margin, y: SCREEN_HEIGHT - 100, width: 150, height: 40, action: 'showHistory' },
      { text: '年龄增长', x: margin + 160, y: SCREEN_HEIGHT - 100, width: 150, height: 40, action: 'ageUp' }
    ];

    this.renderButtons();
  }

  // 渲染顶部信息栏
  renderTopBar() {
    const margin = this.styles.spacing.margin;
    const stageInfo = GameState.getCurrentStageInfo();
    
    // 背景
    this.ctx.fillStyle = this.styles.colors.primary;
    this.ctx.fillRect(0, 0, SCREEN_WIDTH, 60);
    
    // 文字
    this.drawText(`年龄: ${GameState.age}岁`, margin, 25, this.styles.fonts.body, '#FFFFFF');
    this.drawText(`人生阶段: ${stageInfo?.name || '未知'}`, margin, 45, this.styles.fonts.small, '#FFFFFF');
    this.drawText(`所在地: ${GameState.currentLocation}`, SCREEN_WIDTH - margin, 25, this.styles.fonts.body, '#FFFFFF', 'right');
    this.drawText(`名声: ${GameState.fame}`, SCREEN_WIDTH - margin, 45, this.styles.fonts.small, '#FFFFFF', 'right');
  }

  // 渲染属性面板
  renderAttributePanel() {
    const startY = 80;
    const margin = this.styles.spacing.margin;
    const barWidth = SCREEN_WIDTH - margin * 2;
    const barHeight = 20;
    
    const attributes = [
      { name: '健康', value: GameState.health, color: '#FF6B6B' },
      { name: '才智', value: GameState.intelligence, color: '#4ECDC4' },
      { name: '道德', value: GameState.morality, color: '#45B7D1' },
      { name: '财富', value: GameState.wealth, color: '#FFA726' },
      { name: '诗才', value: GameState.poetry, color: '#AB47BC' }
    ];

    attributes.forEach((attr, index) => {
      const y = startY + index * 35;
      
      // 属性名称
      this.drawText(attr.name, margin, y, this.styles.fonts.body, this.styles.colors.text);
      
      // 属性值
      this.drawText(attr.value.toString(), SCREEN_WIDTH - margin - 30, y, this.styles.fonts.body, this.styles.colors.text, 'right');
      
      // 属性条背景
      this.ctx.fillStyle = '#E0E0E0';
      this.ctx.fillRect(margin + 60, y - 8, barWidth - 120, barHeight);
      
      // 属性条
      this.ctx.fillStyle = attr.color;
      this.ctx.fillRect(margin + 60, y - 8, (barWidth - 120) * (attr.value / 100), barHeight);
    });
  }

  // 渲染当前状态
  renderCurrentStatus() {
    const startY = 280;
    const margin = this.styles.spacing.margin;
    const stageInfo = GameState.getCurrentStageInfo();
    
    this.drawText('当前状态', margin, startY, this.styles.fonts.subtitle, this.styles.colors.primary);
    this.drawText(stageInfo?.description || '人生旅程继续...', margin, startY + 30, this.styles.fonts.body, this.styles.colors.text);
    
    // 显示最近的成就
    if (GameState.achievements.length > 0) {
      const latestAchievement = GameState.achievements[GameState.achievements.length - 1];
      this.drawText(`最新成就: ${latestAchievement}`, margin, startY + 60, this.styles.fonts.body, this.styles.colors.secondary);
    }
  }

  // 渲染按钮
  renderButtons() {
    this.buttons.forEach((button, index) => {
      const isSelected = index === this.selectedButton;
      const isHovered = isSelected && Math.sin(this.animationFrame * 0.1) > 0;
      
      // 按钮背景
      this.ctx.fillStyle = isHovered ? this.styles.colors.highlight : 
                          isSelected ? this.styles.colors.secondary : this.styles.colors.accent;
      this.ctx.fillRect(button.x, button.y, button.width, button.height);
      
      // 按钮边框
      this.ctx.strokeStyle = this.styles.colors.primary;
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(button.x, button.y, button.width, button.height);
      
      // 按钮文字
      const textColor = isSelected ? '#FFFFFF' : this.styles.colors.text;
      this.drawText(button.text, button.x + button.width / 2, button.y + button.height / 2 + 5, 
                   this.styles.fonts.body, textColor, 'center');
    });
  }

  // 绘制文字
  drawText(text, x, y, font, color, align = 'left') {
    this.ctx.font = font;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    this.ctx.fillText(text, x, y);
  }

  // 处理按钮点击
  handleButtonClick(x, y) {
    for (const button of this.buttons) {
      if (x >= button.x && x <= button.x + button.width &&
          y >= button.y && y <= button.y + button.height) {
        return button.action;
      }
    }
    return null;
  }

  // 处理键盘输入
  handleKeyInput(key) {
    if (key === 'ArrowUp' || key === 'w') {
      this.selectedButton = Math.max(0, this.selectedButton - 1);
    } else if (key === 'ArrowDown' || key === 's') {
      this.selectedButton = Math.min(this.buttons.length - 1, this.selectedButton + 1);
    } else if (key === 'Enter' || key === ' ') {
      if (this.buttons[this.selectedButton]) {
        return this.buttons[this.selectedButton].action;
      }
    }
    return null;
  }

  // 切换界面
  switchScreen(screenName) {
    this.currentScreen = screenName;
    this.selectedButton = 0;
    this.buttons = [];
  }

  // 获取当前界面
  getCurrentScreen() {
    return this.currentScreen;
  }

  // 设置当前事件
  setCurrentEvent(event) {
    this.currentEvent = event;
  }

  // 绘制多行文字
  drawMultilineText(text, x, y, maxWidth, font, color, align = 'left') {
    this.ctx.font = font;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;

    const words = text.split(' ');
    let line = '';
    let currentY = y;

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i] + ' ';
      const metrics = this.ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        this.ctx.fillText(line, x, currentY);
        line = words[i] + ' ';
        currentY += this.styles.spacing.lineHeight;
      } else {
        line = testLine;
      }
    }
    this.ctx.fillText(line, x, currentY);
  }

  // 格式化效果文字
  formatEffects(effects) {
    const effectTexts = [];
    for (const [attr, value] of Object.entries(effects)) {
      const sign = value > 0 ? '+' : '';
      const attrName = this.getAttributeDisplayName(attr);
      effectTexts.push(`${attrName}${sign}${value}`);
    }
    return effectTexts.join(', ');
  }

  // 获取属性显示名称
  getAttributeDisplayName(attr) {
    const names = {
      health: '健康',
      intelligence: '才智',
      morality: '道德',
      wealth: '财富',
      fame: '名声',
      poetry: '诗才'
    };
    return names[attr] || attr;
  }

  // 检查点击位置是否在按钮内
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
  }

  // 获取按钮数量
  getButtonCount() {
    return this.buttons.length;
  }

  // 重置选中的按钮
  resetSelection() {
    this.selectedButton = 0;
  }
}

export default UIManager;
