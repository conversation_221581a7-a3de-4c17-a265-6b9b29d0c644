class ResourceManager {
  constructor() {
    this.images = {};
    this.sounds = {};
    this.loaded = false;
    this.loadingProgress = 0;
    this.totalResources = 0;
    this.loadedResources = 0;
  }

  // 预加载所有资源
  async preloadResources() {
    const imageResources = [
      { name: 'background', src: 'images/bg.jpg' },
      { name: 'ui_panel', src: 'images/ui_panel.png' },
      { name: 'button_normal', src: 'images/button_normal.png' },
      { name: 'button_hover', src: 'images/button_hover.png' },
      { name: 'libai_portrait', src: 'images/libai_portrait.png' }
    ];

    const soundResources = [
      { name: 'bgm', src: 'audio/bgm.mp3' },
      { name: 'click', src: 'audio/click.mp3' },
      { name: 'event', src: 'audio/event.mp3' }
    ];

    this.totalResources = imageResources.length + soundResources.length;

    // 加载图片资源
    const imagePromises = imageResources.map(resource => this.loadImage(resource));
    
    // 加载音频资源
    const soundPromises = soundResources.map(resource => this.loadSound(resource));

    try {
      await Promise.all([...imagePromises, ...soundPromises]);
      this.loaded = true;
      console.log('所有资源加载完成');
    } catch (error) {
      console.error('资源加载失败:', error);
    }
  }

  // 加载单个图片
  loadImage(resource) {
    return new Promise((resolve, reject) => {
      if (typeof wx !== 'undefined') {
        // 微信小游戏环境
        const image = wx.createImage();
        image.onload = () => {
          this.images[resource.name] = image;
          this.loadedResources++;
          this.updateProgress();
          resolve(image);
        };
        image.onerror = reject;
        image.src = resource.src;
      } else {
        // 浏览器环境
        const image = new Image();
        image.onload = () => {
          this.images[resource.name] = image;
          this.loadedResources++;
          this.updateProgress();
          resolve(image);
        };
        image.onerror = reject;
        image.src = resource.src;
      }
    });
  }

  // 加载单个音频
  loadSound(resource) {
    return new Promise((resolve, reject) => {
      if (typeof wx !== 'undefined') {
        // 微信小游戏环境
        const audio = wx.createInnerAudioContext();
        audio.src = resource.src;
        audio.onCanplay = () => {
          this.sounds[resource.name] = audio;
          this.loadedResources++;
          this.updateProgress();
          resolve(audio);
        };
        audio.onError = reject;
      } else {
        // 浏览器环境
        const audio = new Audio();
        audio.oncanplaythrough = () => {
          this.sounds[resource.name] = audio;
          this.loadedResources++;
          this.updateProgress();
          resolve(audio);
        };
        audio.onerror = reject;
        audio.src = resource.src;
      }
    });
  }

  // 更新加载进度
  updateProgress() {
    this.loadingProgress = this.loadedResources / this.totalResources;
  }

  // 获取图片资源
  getImage(name) {
    return this.images[name] || null;
  }

  // 获取音频资源
  getSound(name) {
    return this.sounds[name] || null;
  }

  // 播放音效
  playSound(name, loop = false) {
    const sound = this.getSound(name);
    if (sound) {
      if (typeof wx !== 'undefined') {
        sound.loop = loop;
        sound.play();
      } else {
        sound.loop = loop;
        sound.currentTime = 0;
        sound.play().catch(e => console.log('音频播放失败:', e));
      }
    }
  }

  // 停止音效
  stopSound(name) {
    const sound = this.getSound(name);
    if (sound) {
      if (typeof wx !== 'undefined') {
        sound.stop();
      } else {
        sound.pause();
        sound.currentTime = 0;
      }
    }
  }

  // 设置音量
  setVolume(name, volume) {
    const sound = this.getSound(name);
    if (sound) {
      sound.volume = Math.max(0, Math.min(1, volume));
    }
  }

  // 检查资源是否已加载
  isLoaded() {
    return this.loaded;
  }

  // 获取加载进度
  getProgress() {
    return this.loadingProgress;
  }

  // 创建占位图片（当资源未加载时使用）
  createPlaceholderImage(width, height, color = '#CCCCCC') {
    if (typeof wx !== 'undefined') {
      // 微信小游戏环境下创建临时canvas作为占位图
      const canvas = wx.createCanvas();
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = color;
      ctx.fillRect(0, 0, width, height);
      return canvas;
    } else {
      // 浏览器环境
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = color;
      ctx.fillRect(0, 0, width, height);
      return canvas;
    }
  }

  // 绘制图片（带有错误处理）
  drawImage(ctx, imageName, x, y, width = null, height = null) {
    const image = this.getImage(imageName);
    if (image) {
      if (width !== null && height !== null) {
        ctx.drawImage(image, x, y, width, height);
      } else {
        ctx.drawImage(image, x, y);
      }
    } else {
      // 绘制占位矩形
      ctx.fillStyle = '#E0E0E0';
      ctx.fillRect(x, y, width || 100, height || 100);
      ctx.fillStyle = '#999999';
      ctx.font = '12px serif';
      ctx.textAlign = 'center';
      ctx.fillText('图片加载中...', x + (width || 100) / 2, y + (height || 100) / 2);
    }
  }

  // 清理资源
  cleanup() {
    // 停止所有音频
    for (const sound of Object.values(this.sounds)) {
      if (typeof wx !== 'undefined') {
        sound.destroy();
      } else {
        sound.pause();
      }
    }
    
    this.images = {};
    this.sounds = {};
    this.loaded = false;
    this.loadingProgress = 0;
    this.totalResources = 0;
    this.loadedResources = 0;
  }
}

// 创建全局资源管理器实例
const resourceManager = new ResourceManager();

export default resourceManager;
