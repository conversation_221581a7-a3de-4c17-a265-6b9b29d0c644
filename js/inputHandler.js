class InputHandler {
  constructor(mainInstance) {
    this.main = mainInstance;
    this.init();
  }

  init() {
    // 监听键盘输入
    window.addEventListener('keydown', (e) => {
      this.main.handleKeyInput(e.key);
    });

    // 监听触摸/点击输入（微信小游戏）
    if (typeof wx !== 'undefined') {
      wx.onTouchStart((res) => {
        if (res.touches && res.touches.length > 0) {
          const touch = res.touches[0];
          this.main.handleClick(touch.clientX, touch.clientY);
        }
      });
    } else {
      // 浏览器环境的点击监听
      canvas.addEventListener('click', (e) => {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        this.main.handleClick(x, y);
      });
    }
  }
}

export default InputHandler;