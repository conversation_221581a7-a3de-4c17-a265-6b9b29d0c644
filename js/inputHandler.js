import Main from './main';

class InputHandler {
  constructor(mainInstance) {
    this.main = mainInstance;
    this.init();
  }

  init() {
    // 监听键盘数字键输入，触发对应事件
    window.addEventListener('keydown', (e) => {
      const key = e.key;
      if (key >= '1' && key <= '9') {
        const index = parseInt(key, 10) - 1;
        this.main.triggerEvent(index);
      }
    });
  }
}

export default InputHandler;