import './render'; // 初始化Canvas
import './render'; // 初始化Canvas
import GameState from './gameState'; // 导入游戏状态管理
import EventManager from './eventManager'; // 导入事件管理器

const ctx = canvas.getContext('2d'); // 获取canvas的2D绘图上下文

/**
 * 游戏主函数
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID

  constructor() {
    this.start();
    this.availableEvents = [];
  }

  /**
   * 开始或重启游戏
   */
  start() {
    GameState.reset(); // 重置游戏状态
    this.availableEvents = EventManager.getAvailableEvents();
    cancelAnimationFrame(this.aniId); // 清除上一局的动画
    this.aniId = requestAnimationFrame(this.loop.bind(this)); // 开始新的动画循环
  }

  /**
   * 渲染游戏界面
   */
  render() {
    ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布

    // 这里可以根据GameState的状态渲染不同的界面
    ctx.fillStyle = '#000';
    ctx.font = '24px serif';
    ctx.fillText(`年龄: ${GameState.age}`, 20, 40);
    ctx.fillText(`健康: ${GameState.health}`, 20, 80);
    ctx.fillText(`才智: ${GameState.intelligence}`, 20, 120);
    ctx.fillText(`道德: ${GameState.morality}`, 20, 160);
    ctx.fillText(`财富: ${GameState.wealth}`, 20, 200);
    ctx.fillText(`人生阶段: ${GameState.lifeStage}`, 20, 240);

    // 渲染可触发事件
    ctx.fillText('可触发事件:', 20, 280);
    this.availableEvents.forEach((event, index) => {
      ctx.fillText(`${index + 1}. ${event.description}`, 40, 320 + index * 40);
    });
  }

  // 游戏逻辑更新主函数
  update() {
    // 这里可以添加游戏逻辑更新，比如年龄增长等
    GameState.age += 1;
    if (GameState.age > 80) {
      // 游戏结束逻辑
      cancelAnimationFrame(this.aniId);
      alert('游戏结束，感谢游玩！');
    }
  }

  // 触发事件
  triggerEvent(index) {
    if (index >= 0 && index < this.availableEvents.length) {
      const event = this.availableEvents[index];
      EventManager.triggerEvent(event.id);
      this.availableEvents = EventManager.getAvailableEvents();
    }
  }

  // 实现游戏帧循环
  loop() {
    this.update(); // 更新游戏逻辑
    this.render(); // 渲染游戏画面

    // 请求下一帧动画
    this.aniId = requestAnimationFrame(this.loop.bind(this));
  }
}
