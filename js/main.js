import './render'; // 初始化Canvas
import GameState from './gameState'; // 导入游戏状态管理
import EventManager from './eventManager'; // 导入事件管理器
import UIManager from './ui/uiManager'; // 导入UI管理器
import resourceManager from './resourceManager'; // 导入资源管理器

const ctx = canvas.getContext('2d'); // 获取canvas的2D绘图上下文

/**
 * 游戏主函数
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID

  constructor() {
    this.uiManager = new UIManager(ctx);
    this.eventManager = new EventManager();
    this.gameState = GameState;
    this.currentEvent = null;
    this.isGameRunning = false;
    this.resourcesLoaded = false;

    this.initGame();
  }

  // 初始化游戏
  async initGame() {
    // 显示加载界面
    this.uiManager.switchScreen('loading');

    // 预加载资源
    try {
      await resourceManager.preloadResources();
      this.resourcesLoaded = true;
      console.log('资源加载完成');
    } catch (error) {
      console.error('资源加载失败:', error);
      this.resourcesLoaded = false;
    }

    this.start();
  }

  /**
   * 开始或重启游戏
   */
  start() {
    this.gameState.reset(); // 重置游戏状态
    this.eventManager.reset(); // 重置事件管理器
    this.uiManager.switchScreen('start'); // 切换到开始界面
    this.isGameRunning = true;

    cancelAnimationFrame(this.aniId); // 清除上一局的动画
    this.aniId = requestAnimationFrame(this.loop.bind(this)); // 开始新的动画循环
  }

  /**
   * 渲染游戏界面
   */
  render() {
    this.uiManager.render();
  }

  // 游戏逻辑更新主函数
  update() {
    // 检查游戏结束条件
    if (this.gameState.age >= 80 || this.gameState.health <= 0) {
      this.endGame();
      return;
    }

    // 检查是否有强制事件需要触发
    const mandatoryEvents = this.eventManager.getMandatoryEvents();
    if (mandatoryEvents.length > 0 && this.uiManager.getCurrentScreen() === 'main') {
      this.triggerEvent(mandatoryEvents[0]);
    }
  }

  // 处理用户操作
  handleAction(action) {
    switch (action) {
      case 'startNewGame':
        this.startNewGame();
        break;
      case 'loadGame':
        this.loadGame();
        break;
      case 'showEvents':
        this.showAvailableEvents();
        break;
      case 'showAttributes':
        this.uiManager.switchScreen('attributes');
        break;
      case 'showHistory':
        this.uiManager.switchScreen('history');
        break;
      case 'ageUp':
        this.ageUp();
        break;
      case 'backToMain':
        this.uiManager.switchScreen('main');
        break;
      case 'restart':
        this.start();
        break;
      default:
        if (action.startsWith('selectChoice_')) {
          const choiceIndex = parseInt(action.split('_')[1]);
          this.selectEventChoice(choiceIndex);
        }
        break;
    }
  }

  // 开始新游戏
  startNewGame() {
    this.gameState.reset();
    this.eventManager.reset();
    this.gameState.gamePhase = 'playing';
    this.uiManager.switchScreen('main');
  }

  // 加载游戏
  loadGame() {
    if (this.gameState.loadGame()) {
      this.gameState.gamePhase = 'playing';
      this.uiManager.switchScreen('main');
    } else {
      // 如果没有存档，开始新游戏
      this.startNewGame();
    }
  }

  // 显示可用事件
  showAvailableEvents() {
    const availableEvents = this.eventManager.getAvailableEvents();
    if (availableEvents.length > 0) {
      this.currentEvent = availableEvents[0]; // 选择第一个可用事件
      this.uiManager.setCurrentEvent(this.currentEvent);
      this.uiManager.switchScreen('event');
    } else {
      // 没有可用事件，显示提示
      this.uiManager.setCurrentEvent(null);
      this.uiManager.switchScreen('event');
    }
  }

  // 触发事件
  triggerEvent(event) {
    this.currentEvent = event;
    this.uiManager.setCurrentEvent(event);
    this.uiManager.switchScreen('event');
  }

  // 选择事件选项
  selectEventChoice(choiceIndex) {
    if (this.currentEvent) {
      const result = this.eventManager.triggerEvent(this.currentEvent.id, choiceIndex);
      if (result) {
        // 显示选择结果
        this.showEventResult(result);
        // 保存游戏状态
        this.gameState.saveGame();
        // 返回主界面
        setTimeout(() => {
          this.uiManager.switchScreen('main');
        }, 2000);
      }
    }
  }

  // 显示事件结果
  showEventResult(result) {
    // 这里可以添加结果显示动画
    console.log('事件结果:', result);
  }

  // 年龄增长
  ageUp() {
    this.gameState.ageUp();
    this.gameState.saveGame();
  }

  // 结束游戏
  endGame() {
    this.gameState.gamePhase = 'ended';
    this.calculateEnding();
    this.uiManager.switchScreen('ending');
    this.isGameRunning = false;
  }

  // 计算结局
  calculateEnding() {
    const { poetry, fame, wealth, morality, intelligence } = this.gameState;

    let endingType = null;

    if (poetry >= 80 && fame >= 70) {
      endingType = {
        title: '诗仙传说',
        description: '你的诗才震古烁今，被后世尊为诗仙，千古流传。'
      };
    } else if (morality >= 80 && wealth <= 30) {
      endingType = {
        title: '隐世高人',
        description: '你淡泊名利，品德高尚，成为一代隐士典范。'
      };
    } else if (intelligence >= 70 && wealth >= 60) {
      endingType = {
        title: '文官显贵',
        description: '你凭借才智在官场获得成功，位高权重。'
      };
    } else if (wealth >= 80) {
      endingType = {
        title: '富商巨贾',
        description: '你善于经营，积累了巨大财富，成为一方富豪。'
      };
    } else {
      endingType = {
        title: '平凡一生',
        description: '你过着平凡而充实的生活，虽无大成就，但也无大遗憾。'
      };
    }

    this.gameState.endingType = endingType;
  }

  // 处理键盘输入
  handleKeyInput(key) {
    const action = this.uiManager.handleKeyInput(key);
    if (action) {
      this.handleAction(action);
    }
  }

  // 处理点击输入
  handleClick(x, y) {
    const action = this.uiManager.handleButtonClick(x, y);
    if (action) {
      this.handleAction(action);
    }
  }

  // 实现游戏帧循环
  loop() {
    if (this.isGameRunning) {
      this.update(); // 更新游戏逻辑
    }
    this.render(); // 渲染游戏画面

    // 请求下一帧动画
    this.aniId = requestAnimationFrame(this.loop.bind(this));
  }
}
