class GameState {
  constructor() {
    this.age = 0; // 当前年龄
    this.health = 100; // 健康指数
    this.intelligence = 100; // 才智指数
    this.morality = 100; // 道德指数
    this.wealth = 0; // 财富
    this.lifeStage = 'childhood'; // 人生阶段，如childhood, youth, adulthood, oldage
    this.history = [];
  }

  // 更新玩家属性
  updateAttribute(attr, value) {
    if (this.hasOwnProperty(attr)) {
      this[attr] += value;
      if (this[attr] < 0) this[attr] = 0;
      if (this[attr] > 100) this[attr] = 100;
    }
  }

  // 设置人生阶段
  setLifeStage(stage) {
    this.lifeStage = stage;
  }

  // 记录事件
  recordEvent(event) {
    this.history.push(event);
  }

  // 重置状态
  reset() {
    this.age = 0;
    this.health = 100;
    this.intelligence = 100;
    this.morality = 100;
    this.wealth = 0;
    this.lifeStage = 'childhood';
    this.history = [];
  }
}

export default new GameState();