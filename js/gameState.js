class GameState {
  constructor() {
    this.age = 0; // 当前年龄
    this.health = 100; // 健康指数 (0-100)
    this.intelligence = 50; // 才智指数 (0-100)
    this.morality = 50; // 道德指数 (0-100)
    this.wealth = 10; // 财富 (0-100)
    this.fame = 0; // 名声 (0-100)
    this.poetry = 20; // 诗才 (0-100) - 李白特有属性
    this.lifeStage = 'childhood'; // 人生阶段
    this.history = []; // 事件历史
    this.achievements = []; // 成就列表
    this.relationships = {}; // 人际关系
    this.currentLocation = '绵州昌隆'; // 当前所在地
    this.gamePhase = 'start'; // 游戏阶段: start, playing, ended
    this.endingType = null; // 结局类型
    this.yearsPassed = 0; // 已过年数
    this.lastEventId = null; // 上次触发的事件ID
  }

  // 人生阶段定义
  static LIFE_STAGES = {
    childhood: { name: '童年期', ageRange: [0, 15], description: '天真烂漫，初学诗书' },
    youth: { name: '青年期', ageRange: [16, 25], description: '意气风发，游历四方' },
    adulthood: { name: '壮年期', ageRange: [26, 45], description: '建功立业，诗名远扬' },
    middleAge: { name: '中年期', ageRange: [46, 60], description: '人生巅峰，或起或落' },
    oldAge: { name: '晚年期', ageRange: [61, 80], description: '回首往昔，传承后世' }
  };

  // 属性限制
  static ATTRIBUTE_LIMITS = {
    health: [0, 100],
    intelligence: [0, 100],
    morality: [0, 100],
    wealth: [0, 100],
    fame: [0, 100],
    poetry: [0, 100]
  };

  // 更新玩家属性
  updateAttribute(attr, value) {
    if (this.hasOwnProperty(attr) && GameState.ATTRIBUTE_LIMITS[attr]) {
      const [min, max] = GameState.ATTRIBUTE_LIMITS[attr];
      this[attr] = Math.max(min, Math.min(max, this[attr] + value));

      // 触发属性变化事件
      this.onAttributeChange(attr, value);
    }
  }

  // 属性变化回调
  onAttributeChange(attr, value) {
    // 记录重要的属性变化
    if (Math.abs(value) >= 10) {
      this.recordEvent({
        type: 'attribute_change',
        attribute: attr,
        change: value,
        newValue: this[attr],
        age: this.age
      });
    }
  }

  // 根据年龄更新人生阶段
  updateLifeStage() {
    for (const [stage, info] of Object.entries(GameState.LIFE_STAGES)) {
      const [minAge, maxAge] = info.ageRange;
      if (this.age >= minAge && this.age <= maxAge) {
        if (this.lifeStage !== stage) {
          this.lifeStage = stage;
          this.recordEvent({
            type: 'life_stage_change',
            newStage: stage,
            stageName: info.name,
            age: this.age
          });
        }
        break;
      }
    }
  }

  // 设置人生阶段
  setLifeStage(stage) {
    if (GameState.LIFE_STAGES[stage]) {
      this.lifeStage = stage;
    }
  }

  // 记录事件
  recordEvent(event) {
    const eventRecord = {
      ...event,
      timestamp: Date.now(),
      age: this.age,
      id: this.history.length + 1
    };
    this.history.push(eventRecord);
  }

  // 添加成就
  addAchievement(achievement) {
    if (!this.achievements.includes(achievement)) {
      this.achievements.push(achievement);
      this.recordEvent({
        type: 'achievement',
        achievement: achievement,
        age: this.age
      });
    }
  }

  // 更新人际关系
  updateRelationship(person, value) {
    if (!this.relationships[person]) {
      this.relationships[person] = 0;
    }
    this.relationships[person] = Math.max(-100, Math.min(100, this.relationships[person] + value));
  }

  // 改变位置
  changeLocation(location) {
    if (this.currentLocation !== location) {
      this.recordEvent({
        type: 'location_change',
        from: this.currentLocation,
        to: location,
        age: this.age
      });
      this.currentLocation = location;
    }
  }

  // 年龄增长
  ageUp(years = 1) {
    this.age += years;
    this.yearsPassed += years;
    this.updateLifeStage();

    // 年龄增长带来的自然属性变化
    if (this.age > 50) {
      this.updateAttribute('health', -1); // 年老体衰
    }
    if (this.age > 30) {
      this.updateAttribute('intelligence', 1); // 阅历增长
    }
  }

  // 获取当前人生阶段信息
  getCurrentStageInfo() {
    return GameState.LIFE_STAGES[this.lifeStage] || null;
  }

  // 检查是否满足条件
  checkCondition(condition) {
    if (condition.attribute) {
      return this[condition.attribute] >= condition.value;
    }
    if (condition.age) {
      return this.age >= condition.age;
    }
    if (condition.achievement) {
      return this.achievements.includes(condition.achievement);
    }
    if (condition.relationship) {
      return (this.relationships[condition.person] || 0) >= condition.value;
    }
    return true;
  }

  // 重置状态
  reset() {
    this.age = 0;
    this.health = 100;
    this.intelligence = 50;
    this.morality = 50;
    this.wealth = 10;
    this.fame = 0;
    this.poetry = 20;
    this.lifeStage = 'childhood';
    this.history = [];
    this.achievements = [];
    this.relationships = {};
    this.currentLocation = '绵州昌隆';
    this.gamePhase = 'start';
    this.endingType = null;
    this.yearsPassed = 0;
    this.lastEventId = null;
  }

  // 保存游戏状态
  saveGame() {
    const saveData = {
      age: this.age,
      health: this.health,
      intelligence: this.intelligence,
      morality: this.morality,
      wealth: this.wealth,
      fame: this.fame,
      poetry: this.poetry,
      lifeStage: this.lifeStage,
      history: this.history,
      achievements: this.achievements,
      relationships: this.relationships,
      currentLocation: this.currentLocation,
      gamePhase: this.gamePhase,
      endingType: this.endingType,
      yearsPassed: this.yearsPassed,
      lastEventId: this.lastEventId,
      saveTime: Date.now()
    };
console.log(saveData);
    try {
      wx.setStorage('libaigame_save', saveData);
      return true;
    } catch (e) {
      console.error('保存游戏失败:', e);
      return false;
    }
  }

  // 加载游戏状态
  loadGame() {
    try {
      const saveData = wx.getStorageSync('libaigame_save');
      if (saveData) {
        Object.assign(this, saveData);
        return true;
      }
    } catch (e) {
      console.error('加载游戏失败:', e);
    }
    return false;
  }

  // 获取游戏统计信息
  getGameStats() {
    return {
      totalEvents: this.history.length,
      achievements: this.achievements.length,
      relationships: Object.keys(this.relationships).length,
      currentStage: this.getCurrentStageInfo()?.name || '未知',
      playTime: this.yearsPassed
    };
  }
}

export default new GameState();