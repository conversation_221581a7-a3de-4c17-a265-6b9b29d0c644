// 简单的游戏启动脚本，用于测试
console.log('正在启动"如果能重来"李白人生模拟游戏...');

// 检查基本的游戏模块
try {
    // 模拟微信小游戏环境
    global.GameGlobal = {};
    
    // 创建一个模拟的canvas环境
    const mockCanvas = {
        width: 400,
        height: 600,
        getContext: () => ({
            clearRect: () => {},
            fillRect: () => {},
            strokeRect: () => {},
            fillText: () => {},
            drawImage: () => {},
            createLinearGradient: () => ({
                addColorStop: () => {}
            }),
            measureText: () => ({ width: 100 }),
            save: () => {},
            restore: () => {},
            translate: () => {},
            rotate: () => {},
            scale: () => {}
        })
    };
    
    global.canvas = mockCanvas;
    
    // 模拟wx对象
    global.wx = {
        createCanvas: () => mockCanvas,
        getWindowInfo: () => ({
            screenWidth: 400,
            screenHeight: 600
        }),
        setStorageSync: (key, data) => {
            console.log(`保存数据: ${key}`, data);
        },
        getStorageSync: (key) => {
            console.log(`读取数据: ${key}`);
            return null;
        },
        createImage: () => ({
            onload: null,
            onerror: null,
            src: ''
        }),
        createInnerAudioContext: () => ({
            src: '',
            onCanplay: null,
            onError: null,
            play: () => {},
            stop: () => {},
            destroy: () => {}
        })
    };
    
    console.log('✓ 模拟环境设置完成');
    
    // 测试GameState
    const GameState = require('./js/gameState.js').default;
    console.log('✓ GameState模块加载成功');
    console.log('初始游戏状态:', {
        age: GameState.age,
        health: GameState.health,
        poetry: GameState.poetry
    });
    
    // 测试EventManager
    const EventManager = require('./js/eventManager.js').default;
    console.log('✓ EventManager模块加载成功');
    
    // 测试获取可用事件
    const availableEvents = EventManager.getAvailableEvents();
    console.log(`可用事件数量: ${availableEvents.length}`);
    if (availableEvents.length > 0) {
        console.log('第一个事件:', availableEvents[0].title);
    }
    
    // 测试ResourceManager
    const resourceManager = require('./js/resourceManager.js').default;
    console.log('✓ ResourceManager模块加载成功');
    
    console.log('\n🎮 游戏核心模块测试完成！');
    console.log('📝 游戏功能概览:');
    console.log('   - 李白人生模拟系统');
    console.log('   - 多阶段人生选择');
    console.log('   - 属性系统（健康、才智、道德、财富、名声、诗才）');
    console.log('   - 事件系统（关键事件、随机事件、选择事件）');
    console.log('   - 多种人生结局');
    console.log('   - 古风UI界面');
    console.log('   - 存档系统');
    
    console.log('\n🚀 请在浏览器中打开 test.html 来体验完整游戏！');
    
} catch (error) {
    console.error('❌ 游戏启动测试失败:', error.message);
    console.error('详细错误:', error);
}
