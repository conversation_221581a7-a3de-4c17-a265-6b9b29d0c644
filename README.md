# 如果能重来 - 李白人生模拟游戏

一个以李白为主角的人生选择模拟游戏，玩家将体验李白的传奇人生，在关键时刻做出决策，走向不同的人生结局。

## 🎮 游戏特色

### 核心玩法
- **人生模拟**: 从童年到晚年，体验李白完整的人生历程
- **选择决策**: 在关键人生节点做出选择，影响后续发展
- **属性系统**: 健康、才智、道德、财富、名声、诗才六大属性
- **多重结局**: 根据选择和属性走向不同的人生结局

### 人生阶段
- **童年期** (0-15岁): 基础教育和性格养成
- **青年期** (16-25岁): 求学游历，初入社会
- **壮年期** (26-45岁): 事业发展，人生选择
- **中年期** (46-60岁): 成就巅峰或人生转折
- **晚年期** (61-80岁): 总结人生，传承后世

### 结局类型
- **诗仙传说**: 诗才震古烁今，千古流传
- **隐世高人**: 淡泊名利，品德高尚
- **文官显贵**: 凭借才智在官场获得成功
- **富商巨贾**: 善于经营，积累巨大财富
- **平凡一生**: 平凡而充实的人生

## 🛠️ 技术架构

### 项目结构
```
├── game.js              # 游戏入口文件
├── game.json            # 游戏配置文件
├── test.html            # 浏览器测试页面
├── js/                  # JavaScript 源码目录
│   ├── main.js          # 主游戏逻辑
│   ├── gameState.js     # 游戏状态管理
│   ├── eventManager.js  # 事件系统
│   ├── resourceManager.js # 资源管理
│   ├── inputHandler.js  # 输入处理
│   ├── render.js        # 渲染初始化
│   └── ui/              # UI系统
│       └── uiManager.js # UI管理器
├── images/              # 图片资源
└── audio/               # 音频资源
```

### 核心系统

#### 游戏状态管理 (GameState)
- 玩家属性管理
- 人生阶段划分
- 历史记录
- 存档系统

#### 事件系统 (EventManager)
- 关键事件：人生重要节点
- 随机事件：基于概率触发
- 选择事件：玩家主动选择
- 连锁事件：基于前置条件

#### UI系统 (UIManager)
- 多界面管理
- 古风UI设计
- 响应式布局
- 动画效果

#### 资源管理 (ResourceManager)
- 图片资源加载
- 音频资源管理
- 加载进度显示
- 错误处理

## 🚀 快速开始

### 在浏览器中测试
1. 启动本地服务器：
   ```bash
   python3 -m http.server 8000
   ```
2. 打开浏览器访问：`http://localhost:8000/test.html`

### 在微信开发者工具中运行
1. 打开微信开发者工具
2. 导入项目目录
3. 点击编译运行

## 🎯 操作说明

### 控制方式
- **鼠标/触摸**: 点击按钮进行操作
- **键盘**: 方向键选择，回车确认

### 游戏界面
- **开始界面**: 新游戏或继续游戏
- **主界面**: 查看属性和当前状态
- **事件界面**: 做出人生选择
- **属性界面**: 详细属性信息
- **历史界面**: 回顾人生历程
- **结局界面**: 查看最终结局

## 📊 属性说明

| 属性 | 说明 | 影响 |
|------|------|------|
| 健康 | 身体状况 | 影响寿命和活动能力 |
| 才智 | 智慧水平 | 影响学习和创作能力 |
| 道德 | 品德修养 | 影响社会声誉和人际关系 |
| 财富 | 经济状况 | 影响生活质量和选择机会 |
| 名声 | 社会声望 | 影响社会地位和机遇 |
| 诗才 | 文学天赋 | 李白特有，影响文学成就 |

## 🎨 游戏特色

### 古风设计
- 古典色彩搭配
- 传统UI元素
- 诗词文化融入
- 历史背景还原

### 深度体验
- 丰富的事件内容
- 真实的历史背景
- 多样的人生选择
- 个性化的结局

## 🔧 开发说明

### 添加新事件
在 `js/eventManager.js` 中的 `events` 对象中添加新事件：

```javascript
{
  id: 'new_event_001',
  type: 'choice_event',
  title: '事件标题',
  description: '事件描述',
  choices: [
    {
      text: '选择一',
      effects: { poetry: 10, fame: 5 },
      consequences: '选择结果描述'
    }
  ],
  conditions: { age: 20 }
}
```

### 自定义UI
修改 `js/ui/uiManager.js` 中的样式配置：

```javascript
this.styles = {
  colors: {
    primary: '#8B4513',    // 主色调
    secondary: '#DAA520',  // 次色调
    // ...
  }
}
```

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的游戏核心系统
- ✅ 李白人生事件数据库
- ✅ 多界面UI系统
- ✅ 属性和选择系统
- ✅ 存档功能
- ✅ 多种结局

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进游戏！

## 📄 许可证

MIT License
