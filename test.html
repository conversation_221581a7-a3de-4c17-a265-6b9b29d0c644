<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>如果能重来 - 李白人生模拟游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #2c2c2c;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: serif;
        }
        
        #gameContainer {
            border: 2px solid #8B4513;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        
        canvas {
            display: block;
            background-color: #F5E6D3;
        }
        
        .info {
            color: white;
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .controls {
            color: white;
            text-align: center;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div>
        <div id="gameContainer">
            <canvas id="gameCanvas" width="400" height="600"></canvas>
        </div>
        <div class="info">
            <h3>如果能重来 - 李白人生模拟游戏</h3>
            <p>体验李白的传奇人生，在关键时刻做出选择</p>
        </div>
        <div class="controls">
            <p>操作说明：点击按钮或使用方向键选择，回车确认</p>
        </div>
    </div>

    <script>
        // 模拟微信小游戏环境的全局变量
        window.GameGlobal = {};
        window.canvas = document.getElementById('gameCanvas');
        
        // 模拟微信小游戏的wx对象（简化版）
        window.wx = {
            createCanvas: () => canvas,
            getWindowInfo: () => ({
                screenWidth: canvas.width,
                screenHeight: canvas.height
            }),
            setStorageSync: (key, data) => {
                localStorage.setItem(key, JSON.stringify(data));
            },
            getStorageSync: (key) => {
                const data = localStorage.getItem(key);
                return data ? JSON.parse(data) : null;
            },
            createImage: () => new Image(),
            createInnerAudioContext: () => new Audio(),
            onTouchStart: (callback) => {
                canvas.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const rect = canvas.getBoundingClientRect();
                    const touches = Array.from(e.touches).map(touch => ({
                        clientX: touch.clientX - rect.left,
                        clientY: touch.clientY - rect.top
                    }));
                    callback({ touches });
                });
            }
        };
    </script>

    <!-- 游戏模块 -->
    <script type="module">
        import Main from './js/main.js';
        import InputHandler from './js/inputHandler.js';

        // 启动游戏
        const mainInstance = new Main();
        new InputHandler(mainInstance);
        
        // 添加调试信息
        window.game = mainInstance;
        console.log('游戏已启动，可以通过 window.game 访问游戏实例');
    </script>
</body>
</html>
